import {useCallback} from 'react';
import {Form} from 'antd';
import {message} from '@panda-design/components';
import {useNavigate} from 'react-router-dom';
import {useSearchParams} from '@panda-design/router';
import {apiPutMCPServer} from '@/api/mcp';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer, loadMCPServer} from '@/regions/mcp/mcpServer';
import {useSpaceLabels} from '@/regions/mcp/mcpSpace';
import {MCPEditTab} from '@/types/mcp/mcp';
import {serverConfigParse} from '../../MCPCreate/hooks';
import {resetTouchedBasePath} from '../regions';
import {useHandleSaveTool} from '../ToolsContent/hooks';
import {setLeavePageNotConfirm} from './utils';

interface UseSaveLogicProps {
    on: () => void;
    off: () => void;
    nextLocation: string;
    onConfirmLeave?: () => void;
}

export const useSaveLogic = ({on, off, nextLocation, onConfirmLeave}: UseSaveLogicProps) => {
    const {validateFields} = Form.useFormInstance();
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const spaceLabels = useSpaceLabels(mcpServer?.workspaceId);
    const {activeTab} = useSearchParams();
    const navigate = useNavigate();
    const saveTool = useHandleSaveTool({on, off});

    return useCallback(
        async () => {
            try {
                on();
                if (activeTab === MCPEditTab.ServerInfo) {
                    const values = await validateFields();
                    const {serverInfo} = values;
                    const {serverConf, labels} = serverInfo;
                    const {serverConfig, serverExtension} = serverConf ?? {};
                    const {serverAuthType, ...restServerExtension} = serverExtension || {};

                    await apiPutMCPServer({
                        mcpServerId,
                        ...mcpServer,
                        ...serverInfo,
                        serverAuthType,
                        labels: spaceLabels?.filter(({id}) =>
                            labels.includes(id)).map(({id, labelValue}) => ({id, labelValue})
                        ),
                        serverConf: {
                            ...mcpServer.serverConf,
                            ...serverConf,
                            serverConfig: serverConfigParse(serverConfig, serverInfo.serverProtocolType),
                            serverExtension: restServerExtension,
                        },
                    });
                    resetTouchedBasePath();
                    await loadMCPServer({mcpServerId});

                    setTimeout(() => {
                        resetTouchedBasePath();
                    }, 100);
                } else if (activeTab === 'tools') {
                    await saveTool();
                }

                resetTouchedBasePath();
                onConfirmLeave?.();
                requestAnimationFrame(() => {
                    navigate(nextLocation);
                });
            } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败，请重试');
            } finally {
                off();
            }
        },
        [
            activeTab, mcpServer, mcpServerId, navigate, nextLocation,
            off, on, saveTool, spaceLabels, validateFields, onConfirmLeave,
        ]
    );
};

interface UseLeaveLogicProps {
    loading: boolean;
    checked: boolean;
    resetForm: () => void;
    onConfirmLeave?: () => void;
    nextLocation: string;
}

export const useLeaveLogic = ({loading, checked, resetForm, onConfirmLeave, nextLocation}: UseLeaveLogicProps) => {
    const navigate = useNavigate();

    return useCallback(
        () => {
            if (loading) {
                return;
            }
            if (checked) {
                setLeavePageNotConfirm();
            }
            resetTouchedBasePath();
            resetForm();
            onConfirmLeave?.();
            requestAnimationFrame(() => {
                navigate(nextLocation);
            });
        },
        [loading, checked, resetForm, onConfirmLeave, navigate, nextLocation]
    );
};
