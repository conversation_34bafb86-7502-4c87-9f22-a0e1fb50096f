import {useCallback} from 'react';
import {FormInstance} from 'antd';
import {MCPServerBase} from '@/types/mcp/mcp';
import {tapIndexAsKey} from '../utils';
import {setSystemUpdating} from '../hooks';

interface UseFormResetProps {
    form: FormInstance;
    mcpServer?: MCPServerBase;
}

export const useFormReset = ({form, mcpServer}: UseFormResetProps) => {
    return useCallback(
        () => {
            if (mcpServer) {
                const serverParams = mcpServer.serverParams || [];
                const serverInfoData = {
                    ...mcpServer,
                    serverConf: {
                        ...mcpServer.serverConf,
                        serverConfig: mcpServer.serverConf?.serverConfig
                            ? JSON.stringify(mcpServer.serverConf.serverConfig, null, 4) : '',
                        serverExtension: {
                            ...mcpServer.serverConf?.serverExtension,
                            serverAuthType: mcpServer.serverAuthType,
                        },
                    },
                    serverParams: tapIndexAsKey(serverParams, 'index'),
                    labels: mcpServer.labels?.map(({id}: {id: number}) => id) || [],
                };

                setSystemUpdating(true);
                try {
                    form.setFieldsValue({
                        serverInfo: serverInfoData,
                        serverSourceType: mcpServer.serverSourceType,
                    });
                } finally {
                    setSystemUpdating(false);
                }
            }
        },
        [form, mcpServer]
    );
};
